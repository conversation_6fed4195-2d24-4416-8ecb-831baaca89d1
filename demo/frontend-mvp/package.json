{"name": "frontend-mvp", "private": true, "version": "0.1.0", "type": "module", "description": "知深学习导师 MVP 前端演示应用", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@chakra-ui/react": "^3.8.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "axios": "^1.7.4", "date-fns": "^3.6.0", "framer-motion": "^11.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.49.3", "react-icons": "^5.4.0", "react-router-dom": "^6.26.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.4.14"}}
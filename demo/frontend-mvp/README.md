# 知深学习导师 - MVP 前端演示应用

## 📖 项目概述

这是"知深学习导师"项目的MVP前端演示应用，基于PRD文档中定义的产品需求构建。该应用展示了一个具备长期主题记忆、且能通过交互式摘要轻松回顾的AI对话体验。

### 🎯 核心价值主张

验证核心假设：**一个具备长期主题记忆、且能通过交互式摘要轻松回顾的AI对话体验，是否能显著提升用户的学习效率和深度，并让用户认为其价值远超于普通聊天机器人。**

## 🚀 功能特性

### ✅ 已实现功能

#### F1 - 主题式会话管理
- ✅ **F1.1**: 创建独立的主题会话
- ✅ **F1.2**: 关联知识源
  - 支持 `.txt`, `.md` 文件上传
  - 支持长文本粘贴输入
  - 多文件合并处理
- ✅ **F1.3**: 主题列表展示（卡片式布局）
- ✅ **F1.4**: 历史主题载入和继续学习

#### F2 - 双栏专注学习界面
- ✅ **F2.1**: 左侧核心对话区域
  - 完整交互历史展示
  - 实时消息发送
  - 消息高亮功能
- ✅ **F2.2**: 右侧流式摘要区域
  - "你/AI"格式摘要展示
  - 实时摘要更新

#### F3 - 核心交互循环
- ✅ **F3.1**: 逻辑回合识别和异步摘要触发
- ✅ **F3.2**: 结构化摘要对象 `{user_summary, assistant_summary}`
- ✅ **F3.3**: 摘要渲染（双身份标识）
- ✅ **F3.4**: 摘要点击跳转和消息高亮

### 🔄 模拟实现功能

#### F4 - Manticore智能上下文引擎（Mock实现）
- 🔄 **F4.1**: 数据持久化模拟
- 🔄 **F4.2**: 主题内记忆模拟

## 🛠️ 技术栈

### 前端技术
- **React 18** + **TypeScript** - 现代化前端框架
- **Vite** - 快速开发构建工具
- **Chakra UI v3** - 现代化UI组件库
- **TanStack Query** - 数据获取和状态管理
- **React Router DOM** - 路由管理
- **React Hook Form** - 表单处理
- **Axios** - HTTP客户端
- **date-fns** - 日期处理

### 开发工具
- **TypeScript** - 类型安全
- **ESLint** - 代码规范
- **Framer Motion** - 动画效果

## 📦 安装和运行

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd demo/frontend-mvp
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 `http://localhost:3001` 启动

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
demo/frontend-mvp/
├── src/
│   ├── components/           # 可复用组件
│   │   ├── TopicCard.tsx    # 主题卡片
│   │   ├── CreateTopicModal.tsx # 创建主题模态框
│   │   ├── FileUploadZone.tsx   # 文件上传组件
│   │   ├── ChatInterface.tsx    # 对话界面
│   │   ├── SummaryPanel.tsx     # 摘要面板
│   │   ├── TopicHeader.tsx      # 主题头部
│   │   ├── Header.tsx           # 应用头部
│   │   └── ErrorFallback.tsx    # 错误边界
│   ├── pages/               # 页面组件
│   │   ├── HomePage.tsx     # 主页（主题列表）
│   │   └── LearningPage.tsx # 学习页面（双栏界面）
│   ├── services/            # API服务
│   │   ├── api.ts          # 通用API客户端
│   │   ├── topicService.ts # 主题相关API
│   │   └── chatService.ts  # 对话相关API
│   ├── types/              # TypeScript类型定义
│   │   └── index.ts        # 核心类型
│   ├── theme.ts            # Chakra UI主题配置
│   ├── App.tsx             # 主应用组件
│   └── main.tsx            # 应用入口
├── package.json            # 项目配置
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目文档
```

## 🎨 设计特色

### 用户体验
- **沉浸式学习界面**: 双栏布局，专注对话和摘要
- **直观的主题管理**: 卡片式主题列表，状态清晰
- **智能交互反馈**: 实时摘要生成，点击跳转
- **响应式设计**: 适配桌面和移动设备

### 视觉设计
- **现代化UI**: 基于Chakra UI的设计系统
- **主题切换**: 支持明暗主题
- **动画效果**: 流畅的交互动画
- **状态指示**: 清晰的加载和错误状态

## 🧪 测试功能

### 基本流程测试
1. **创建主题**: 点击"创建新主题"，上传文件或粘贴文本
2. **开始对话**: 进入学习页面，与AI导师对话
3. **查看摘要**: 观察右侧摘要面板的实时更新
4. **摘要跳转**: 点击摘要卡片，跳转到对应对话
5. **主题管理**: 返回主页，查看主题列表和状态

### Mock数据说明
- 应用使用Mock数据模拟后端API
- 包含示例主题、对话和摘要数据
- 支持基本的CRUD操作模拟

## 🔮 后续开发计划

### Phase 1: 后端集成
- [ ] 集成真实的FastAPI后端
- [ ] 连接Manticore Search引擎
- [ ] 实现文件上传和处理

### Phase 2: 高级功能
- [ ] WebSocket实时通信
- [ ] 高级搜索和过滤
- [ ] 导出学习笔记

### Phase 3: 优化和扩展
- [ ] 性能优化
- [ ] 移动端适配
- [ ] 用户认证系统

## 📝 开发说明

### 代码规范
- 使用TypeScript进行类型检查
- 遵循React Hooks最佳实践
- 组件采用函数式编程风格
- 使用ESLint进行代码规范检查

### 状态管理
- 使用TanStack Query管理服务器状态
- 使用React useState管理本地状态
- 避免过度的状态提升

### 样式管理
- 使用Chakra UI组件系统
- 自定义主题配置
- 响应式设计原则

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**注意**: 这是一个MVP演示应用，主要用于验证产品概念和用户体验。生产环境部署需要额外的安全性和性能优化考虑。

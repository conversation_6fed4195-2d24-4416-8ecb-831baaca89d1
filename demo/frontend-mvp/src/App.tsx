import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, Container, useColorModeValue } from '@chakra-ui/react'
import { ErrorBoundary } from 'react-error-boundary'
import HomePage from './pages/HomePage'
import LearningPage from './pages/LearningPage'
import ErrorFallback from './components/ErrorFallback'
import Header from './components/Header'

function App() {
  const bgColor = useColorModeValue('gray.50', 'gray.900')

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Box minH="100vh" bg={bgColor}>
        <Header />
        <Container maxW="full" p={0}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/topic/:topicId" element={<LearningPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Container>
      </Box>
    </ErrorBoundary>
  )
}

export default App

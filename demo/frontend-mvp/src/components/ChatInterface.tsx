import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Textarea,
  Button,
  Avatar,
  useColorModeValue,
  Flex,
  IconButton,
  Tooltip,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { FiSend, FiUser, FiBot } from 'react-icons/fi'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Message } from '@/types'
import { mockChatService } from '@/services/chatService'

interface ChatInterfaceProps {
  topicId: string
  selectedMessageId: string | null
  onMessageSelect: (messageId: string | null) => void
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  topicId,
  selectedMessageId,
  onMessageSelect,
}) => {
  const [inputValue, setInputValue] = useState('')
  const [isComposing, setIsComposing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messageRefs = useRef<{ [key: string]: HTMLDivElement }>({})
  const queryClient = useQueryClient()

  const bgColor = useColorModeValue('white', 'gray.800')
  const inputBgColor = useColorModeValue('gray.50', 'gray.700')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // 获取对话消息
  const {
    data: messages = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['messages', topicId],
    queryFn: () => mockChatService.getMessages(topicId),
  })

  // 发送消息
  const sendMessageMutation = useMutation({
    mutationFn: (content: string) => mockChatService.sendMessage(topicId, content),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', topicId] })
      setInputValue('')
      scrollToBottom()
    },
  })

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 滚动到指定消息
  const scrollToMessage = (messageId: string) => {
    const element = messageRefs.current[messageId]
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      // 高亮消息
      element.classList.add('message-highlight', 'active')
      setTimeout(() => {
        element.classList.remove('active')
      }, 2000)
    }
  }

  // 处理选中消息变化
  useEffect(() => {
    if (selectedMessageId) {
      scrollToMessage(selectedMessageId)
    }
  }, [selectedMessageId])

  // 自动滚动到底部（新消息时）
  useEffect(() => {
    if (messages.length > 0 && !selectedMessageId) {
      scrollToBottom()
    }
  }, [messages.length, selectedMessageId])

  const handleSendMessage = () => {
    if (!inputValue.trim() || sendMessageMutation.isPending) return
    
    sendMessageMutation.mutate(inputValue.trim())
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleCompositionStart = () => {
    setIsComposing(true)
  }

  const handleCompositionEnd = () => {
    setIsComposing(false)
  }

  if (isLoading) {
    return (
      <Flex h="full" align="center" justify="center">
        <VStack spacing={4}>
          <Spinner size="lg" color="brand.500" />
          <Text color="gray.500">加载对话历史...</Text>
        </VStack>
      </Flex>
    )
  }

  if (error) {
    return (
      <Flex h="full" align="center" justify="center" p={8}>
        <Alert status="error" borderRadius="md" maxW="md">
          <AlertIcon />
          <AlertDescription>加载对话失败，请刷新重试</AlertDescription>
        </Alert>
      </Flex>
    )
  }

  return (
    <Flex direction="column" h="full">
      {/* 消息列表 */}
      <Box flex={1} overflowY="auto" p={6}>
        <VStack spacing={6} align="stretch">
          {messages.length === 0 ? (
            <Box textAlign="center" py={12}>
              <VStack spacing={4}>
                <FiBot size={48} color="gray.400" />
                <Text color="gray.500" fontSize="lg">
                  开始与AI导师对话吧！
                </Text>
                <Text color="gray.400" fontSize="sm">
                  基于你上传的学习材料，AI将为你提供个性化的学习指导
                </Text>
              </VStack>
            </Box>
          ) : (
            messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isSelected={selectedMessageId === message.id}
                onClick={() => onMessageSelect(message.id)}
                ref={(el) => {
                  if (el) messageRefs.current[message.id] = el
                }}
              />
            ))
          )}
          
          {sendMessageMutation.isPending && (
            <MessageBubble
              message={{
                id: 'pending',
                role: 'assistant',
                content: '',
                timestamp: new Date().toISOString(),
              }}
              isLoading
            />
          )}
          
          <div ref={messagesEndRef} />
        </VStack>
      </Box>

      {/* 输入区域 */}
      <Box
        p={6}
        borderTop="1px solid"
        borderColor={borderColor}
        bg={bgColor}
      >
        <HStack spacing={3}>
          <Textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            placeholder="输入你的问题或想法..."
            bg={inputBgColor}
            border="1px solid"
            borderColor={borderColor}
            borderRadius="lg"
            resize="none"
            rows={3}
            maxRows={6}
            _focus={{
              borderColor: 'brand.500',
              boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
            }}
          />
          
          <Tooltip label="发送消息 (Enter)" placement="top" hasArrow>
            <IconButton
              aria-label="发送消息"
              icon={<FiSend />}
              colorScheme="brand"
              size="lg"
              isLoading={sendMessageMutation.isPending}
              isDisabled={!inputValue.trim()}
              onClick={handleSendMessage}
            />
          </Tooltip>
        </HStack>
      </Box>
    </Flex>
  )
}

// 消息气泡组件
interface MessageBubbleProps {
  message: Message
  isSelected?: boolean
  isLoading?: boolean
  onClick?: () => void
}

const MessageBubble = React.forwardRef<HTMLDivElement, MessageBubbleProps>(
  ({ message, isSelected, isLoading, onClick }, ref) => {
    const isUser = message.role === 'user'
    const bgColor = useColorModeValue(
      isUser ? 'brand.500' : 'gray.100',
      isUser ? 'brand.600' : 'gray.700'
    )
    const textColor = useColorModeValue(
      isUser ? 'white' : 'gray.800',
      isUser ? 'white' : 'gray.100'
    )

    return (
      <HStack
        ref={ref}
        spacing={3}
        align="start"
        justify={isUser ? 'flex-end' : 'flex-start'}
        cursor={onClick ? 'pointer' : 'default'}
        onClick={onClick}
        className={isSelected ? 'message-highlight' : ''}
        p={2}
        borderRadius="md"
        transition="all 0.2s"
        _hover={onClick ? { bg: 'gray.50', _dark: { bg: 'gray.700' } } : {}}
      >
        {!isUser && (
          <Avatar size="sm" icon={<FiBot />} bg="brand.500" color="white" />
        )}
        
        <Box
          bg={bgColor}
          color={textColor}
          px={4}
          py={3}
          borderRadius="lg"
          maxW="70%"
          position="relative"
        >
          {isLoading ? (
            <HStack spacing={2}>
              <Spinner size="sm" />
              <Text>AI正在思考...</Text>
            </HStack>
          ) : (
            <Text whiteSpace="pre-wrap" lineHeight="1.5">
              {message.content}
            </Text>
          )}
        </Box>
        
        {isUser && (
          <Avatar size="sm" icon={<FiUser />} bg="gray.500" color="white" />
        )}
      </HStack>
    )
  }
)

MessageBubble.displayName = 'MessageBubble'

export default ChatInterface

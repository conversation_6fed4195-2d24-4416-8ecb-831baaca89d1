import React from 'react'
import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
  useColorModeValue,
} from '@chakra-ui/react'
import { FallbackProps } from 'react-error-boundary'

const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  return (
    <Container maxW="2xl" py={10}>
      <Box
        bg={bgColor}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>应用程序出现错误！</AlertTitle>
              <AlertDescription>
                很抱歉，应用程序遇到了一个意外错误。请尝试刷新页面或联系技术支持。
              </AlertDescription>
            </Box>
          </Alert>

          <VStack spacing={4} align="stretch">
            <Heading size="md" color="red.500">
              错误详情
            </Heading>
            
            <Box>
              <Text fontWeight="semibold" mb={2}>
                错误消息：
              </Text>
              <Code
                display="block"
                whiteSpace="pre-wrap"
                p={3}
                borderRadius="md"
                bg="red.50"
                color="red.800"
                _dark={{
                  bg: 'red.900',
                  color: 'red.200',
                }}
              >
                {error.message}
              </Code>
            </Box>

            {error.stack && (
              <Box>
                <Text fontWeight="semibold" mb={2}>
                  堆栈跟踪：
                </Text>
                <Code
                  display="block"
                  whiteSpace="pre-wrap"
                  p={3}
                  borderRadius="md"
                  bg="gray.50"
                  color="gray.800"
                  fontSize="sm"
                  maxH="200px"
                  overflowY="auto"
                  _dark={{
                    bg: 'gray.800',
                    color: 'gray.200',
                  }}
                >
                  {error.stack}
                </Code>
              </Box>
            )}
          </VStack>

          <VStack spacing={3}>
            <Button
              colorScheme="blue"
              onClick={resetErrorBoundary}
              size="lg"
              width="full"
            >
              重试
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              size="md"
              width="full"
            >
              刷新页面
            </Button>
          </VStack>
        </VStack>
      </Box>
    </Container>
  )
}

export default ErrorFallback

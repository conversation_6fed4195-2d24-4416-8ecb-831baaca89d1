import React from 'react'
import {
  Box,
  Flex,
  <PERSON>ing,
  Button,
  useColorMode,
  useColorModeValue,
  IconButton,
  Container,
  HStack,
  Text,
} from '@chakra-ui/react'
import { MoonIcon, SunIcon } from '@chakra-ui/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { FiHome, FiBook } from 'react-icons/fi'

const Header: React.FC = () => {
  const { colorMode, toggleColorMode } = useColorMode()
  const navigate = useNavigate()
  const location = useLocation()
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const textColor = useColorModeValue('gray.800', 'white')

  const isHomePage = location.pathname === '/'
  const isLearningPage = location.pathname.startsWith('/topic/')

  return (
    <Box
      bg={bgColor}
      borderBottom="1px solid"
      borderColor={borderColor}
      position="sticky"
      top={0}
      zIndex={1000}
      boxShadow="sm"
    >
      <Container maxW="full" px={6}>
        <Flex h={16} alignItems="center" justifyContent="space-between">
          {/* Logo和标题 */}
          <HStack spacing={4}>
            <Heading
              size="lg"
              color="brand.500"
              cursor="pointer"
              onClick={() => navigate('/')}
              _hover={{ color: 'brand.600' }}
              transition="color 0.2s"
            >
              知深学习导师
            </Heading>
            <Text
              fontSize="sm"
              color="gray.500"
              fontWeight="medium"
              bg="brand.50"
              px={2}
              py={1}
              borderRadius="md"
              _dark={{
                bg: 'brand.900',
                color: 'brand.200',
              }}
            >
              MVP Demo
            </Text>
          </HStack>

          {/* 导航和操作按钮 */}
          <HStack spacing={4}>
            {/* 导航按钮 */}
            {!isHomePage && (
              <Button
                leftIcon={<FiHome />}
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
              >
                主题列表
              </Button>
            )}

            {isLearningPage && (
              <HStack spacing={2}>
                <Box
                  w={2}
                  h={2}
                  bg="green.400"
                  borderRadius="full"
                  animation="pulse 2s infinite"
                />
                <Text fontSize="sm" color="green.600" fontWeight="medium">
                  学习中
                </Text>
              </HStack>
            )}

            {/* 主题切换按钮 */}
            <IconButton
              aria-label="切换主题"
              icon={colorMode === 'light' ? <MoonIcon /> : <SunIcon />}
              onClick={toggleColorMode}
              variant="ghost"
              size="sm"
            />
          </HStack>
        </Flex>
      </Container>
    </Box>
  )
}

export default Header

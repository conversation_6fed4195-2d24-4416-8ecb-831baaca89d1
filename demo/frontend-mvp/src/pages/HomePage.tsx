import React, { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Grid,
  GridItem,
  useDisclosure,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  InputGroup,
  InputLeftElement,
  Input,
  Select,
  Flex,
  Badge,
} from '@chakra-ui/react'
import { AddIcon, SearchIcon } from '@chakra-ui/icons'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { FiBook, FiClock, FiMessageCircle } from 'react-icons/fi'
import { mockTopicService } from '@/services/topicService'
import { TopicFilter, Topic } from '@/types'
import CreateTopicModal from '@/components/CreateTopicModal'
import TopicCard from '@/components/TopicCard'

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [filter, setFilter] = useState<TopicFilter>({
    sort_by: 'updated_at',
    sort_order: 'desc',
  })

  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 获取主题列表
  const {
    data: topicsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['topics', filter],
    queryFn: () => mockTopicService.getTopics(filter),
  })

  const handleTopicClick = (topic: Topic) => {
    navigate(`/topic/${topic.id}`)
  }

  const handleFilterChange = (key: keyof TopicFilter, value: any) => {
    setFilter(prev => ({ ...prev, [key]: value }))
  }

  const getStatusColor = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return 'green'
      case 'completed':
        return 'blue'
      case 'paused':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  const getStatusText = (status: Topic['status']) => {
    switch (status) {
      case 'active':
        return '学习中'
      case 'completed':
        return '已完成'
      case 'paused':
        return '已暂停'
      default:
        return '未知'
    }
  }

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* 页面标题和操作 */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
          <VStack align="start" spacing={2}>
            <Heading size="xl" color="brand.500">
              我的学习主题
            </Heading>
            <Text color="gray.600" fontSize="lg">
              开始一个新的学习主题，或继续之前的学习进度
            </Text>
          </VStack>
          
          <Button
            leftIcon={<AddIcon />}
            colorScheme="brand"
            size="lg"
            onClick={onOpen}
            boxShadow="md"
            _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
            transition="all 0.2s"
          >
            创建新主题
          </Button>
        </Flex>

        {/* 搜索和过滤 */}
        <Box
          bg={bgColor}
          border="1px solid"
          borderColor={borderColor}
          borderRadius="lg"
          p={6}
        >
          <Grid templateColumns={{ base: '1fr', md: '2fr 1fr 1fr' }} gap={4}>
            <GridItem>
              <InputGroup>
                <InputLeftElement pointerEvents="none">
                  <SearchIcon color="gray.400" />
                </InputLeftElement>
                <Input
                  placeholder="搜索主题..."
                  value={filter.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </InputGroup>
            </GridItem>
            
            <GridItem>
              <Select
                value={filter.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
              >
                <option value="">所有状态</option>
                <option value="active">学习中</option>
                <option value="completed">已完成</option>
                <option value="paused">已暂停</option>
              </Select>
            </GridItem>
            
            <GridItem>
              <Select
                value={`${filter.sort_by}_${filter.sort_order}`}
                onChange={(e) => {
                  const [sort_by, sort_order] = e.target.value.split('_')
                  handleFilterChange('sort_by', sort_by)
                  handleFilterChange('sort_order', sort_order)
                }}
              >
                <option value="updated_at_desc">最近更新</option>
                <option value="created_at_desc">最新创建</option>
                <option value="title_asc">标题 A-Z</option>
                <option value="title_desc">标题 Z-A</option>
              </Select>
            </GridItem>
          </Grid>
        </Box>

        {/* 主题列表 */}
        <Box>
          {isLoading && (
            <Flex justify="center" py={12}>
              <VStack spacing={4}>
                <Spinner size="xl" color="brand.500" />
                <Text color="gray.500">加载主题列表...</Text>
              </VStack>
            </Flex>
          )}

          {error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <AlertDescription>
                加载主题列表失败，请稍后重试。
                <Button
                  variant="link"
                  colorScheme="red"
                  ml={2}
                  onClick={() => refetch()}
                >
                  重新加载
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {topicsData && topicsData.items.length === 0 && (
            <Box
              textAlign="center"
              py={12}
              bg={bgColor}
              border="1px solid"
              borderColor={borderColor}
              borderRadius="lg"
            >
              <VStack spacing={4}>
                <FiBook size={48} color="gray.400" />
                <Heading size="md" color="gray.500">
                  还没有学习主题
                </Heading>
                <Text color="gray.400">
                  创建你的第一个学习主题，开始AI辅助学习之旅
                </Text>
                <Button
                  leftIcon={<AddIcon />}
                  colorScheme="brand"
                  onClick={onOpen}
                >
                  创建新主题
                </Button>
              </VStack>
            </Box>
          )}

          {topicsData && topicsData.items.length > 0 && (
            <Grid
              templateColumns={{
                base: '1fr',
                md: 'repeat(2, 1fr)',
                lg: 'repeat(3, 1fr)',
              }}
              gap={6}
            >
              {topicsData.items.map((topic) => (
                <TopicCard
                  key={topic.id}
                  topic={topic}
                  onClick={() => handleTopicClick(topic)}
                />
              ))}
            </Grid>
          )}
        </Box>
      </VStack>

      {/* 创建主题模态框 */}
      <CreateTopicModal
        isOpen={isOpen}
        onClose={onClose}
        onSuccess={() => {
          onClose()
          refetch()
        }}
      />
    </Container>
  )
}

export default HomePage

import React, { useState, useEffect, useRef } from 'react'
import {
  Box,
  Grid,
  GridItem,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  Button,
  VStack,
  Text,
  Heading,
} from '@chakra-ui/react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { FiArrowLeft } from 'react-icons/fi'
import { mockTopicService } from '@/services/topicService'
import ChatInterface from '@/components/ChatInterface'
import SummaryPanel from '@/components/SummaryPanel'
import TopicHeader from '@/components/TopicHeader'

const LearningPage: React.FC = () => {
  const { topicId } = useParams<{ topicId: string }>()
  const navigate = useNavigate()
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)
  
  const bgColor = useColorModeValue('gray.50', 'gray.900')

  // 获取主题详情
  const {
    data: topic,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['topic', topicId],
    queryFn: () => mockTopicService.getTopic(topicId!),
    enabled: !!topicId,
  })

  // 处理摘要点击，跳转到对应消息
  const handleSummaryClick = (messageId: string) => {
    setSelectedMessageId(messageId)
    // 滚动到对应消息的逻辑将在ChatInterface中处理
  }

  // 处理返回主题列表
  const handleBackToTopics = () => {
    navigate('/')
  }

  if (isLoading) {
    return (
      <Box
        minH="100vh"
        bg={bgColor}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" />
          <Text color="gray.500">加载学习主题...</Text>
        </VStack>
      </Box>
    )
  }

  if (error || !topic) {
    return (
      <Box
        minH="100vh"
        bg={bgColor}
        display="flex"
        alignItems="center"
        justifyContent="center"
        p={8}
      >
        <VStack spacing={6} maxW="md" textAlign="center">
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <AlertDescription>
              {error ? '加载主题失败' : '主题不存在'}
            </AlertDescription>
          </Alert>
          
          <Button
            leftIcon={<FiArrowLeft />}
            onClick={handleBackToTopics}
            colorScheme="brand"
          >
            返回主题列表
          </Button>
        </VStack>
      </Box>
    )
  }

  return (
    <Box minH="100vh" bg={bgColor}>
      {/* 主题头部信息 */}
      <TopicHeader topic={topic} onBack={handleBackToTopics} />

      {/* 双栏学习界面 */}
      <Grid
        templateColumns={{ base: '1fr', lg: '1fr 400px' }}
        h="calc(100vh - 80px)" // 减去头部高度
        gap={0}
      >
        {/* 左侧：对话区域 */}
        <GridItem
          bg={useColorModeValue('white', 'gray.800')}
          borderRight={{ lg: '1px solid' }}
          borderColor={useColorModeValue('gray.200', 'gray.700')}
          position="relative"
        >
          <ChatInterface
            topicId={topicId!}
            selectedMessageId={selectedMessageId}
            onMessageSelect={setSelectedMessageId}
          />
        </GridItem>

        {/* 右侧：摘要面板 */}
        <GridItem
          bg={useColorModeValue('gray.50', 'gray.900')}
          display={{ base: 'none', lg: 'block' }}
          position="relative"
        >
          <SummaryPanel
            topicId={topicId!}
            onSummaryClick={handleSummaryClick}
            selectedMessageId={selectedMessageId}
          />
        </GridItem>
      </Grid>

      {/* 移动端摘要面板（可切换显示） */}
      <Box display={{ base: 'block', lg: 'none' }}>
        {/* 这里可以添加移动端的摘要切换逻辑 */}
      </Box>
    </Box>
  )
}

export default LearningPage

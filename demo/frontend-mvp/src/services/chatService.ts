import { api } from './api'
import { Message, Conversation, ConversationSummary } from '@/types'

export const chatService = {
  // 获取对话消息
  getMessages: async (topicId: string): Promise<Message[]> => {
    return api.get<Message[]>(`/topics/${topicId}/messages`)
  },

  // 发送消息
  sendMessage: async (topicId: string, content: string): Promise<Message> => {
    return api.post<Message>(`/topics/${topicId}/messages`, { content })
  },

  // 获取对话摘要
  getSummaries: async (topicId: string): Promise<ConversationSummary[]> => {
    return api.get<ConversationSummary[]>(`/topics/${topicId}/summaries`)
  },

  // 获取特定对话
  getConversation: async (topicId: string, conversationId: string): Promise<Conversation> => {
    return api.get<Conversation>(`/topics/${topicId}/conversations/${conversationId}`)
  },
}

// Mock数据和服务（用于开发阶段）
const mockMessages: { [topicId: string]: Message[] } = {
  '1': [
    {
      id: 'msg1',
      role: 'user',
      content: '你好，我想学习React Hooks，特别是useState和useEffect的使用。',
      timestamp: '2024-01-15T09:00:00Z',
      round_id: 'round1',
    },
    {
      id: 'msg2',
      role: 'assistant',
      content: '很高兴帮助你学习React Hooks！useState和useEffect是React中最重要的两个Hook。\n\n让我们从useState开始：\n\n1. **useState的基本用法**：\n```javascript\nconst [count, setCount] = useState(0);\n```\n\n这里创建了一个状态变量count，初始值为0，setCount是更新这个状态的函数。\n\n你想先深入了解useState的哪个方面呢？比如状态更新的机制、函数式更新，还是直接看一些实际例子？',
      timestamp: '2024-01-15T09:01:00Z',
      round_id: 'round1',
    },
    {
      id: 'msg3',
      role: 'user',
      content: '我想了解useState的函数式更新，什么时候需要使用它？',
      timestamp: '2024-01-15T09:05:00Z',
      round_id: 'round2',
    },
    {
      id: 'msg4',
      role: 'assistant',
      content: '很好的问题！函数式更新是useState的一个重要特性。\n\n**什么时候需要函数式更新？**\n\n1. **当新状态依赖于前一个状态时**：\n```javascript\n// ❌ 可能有问题的写法\nsetCount(count + 1);\n\n// ✅ 推荐的函数式更新\nsetCount(prevCount => prevCount + 1);\n```\n\n2. **在异步操作或事件处理中**：\n```javascript\nconst handleMultipleClicks = () => {\n  // 如果快速点击，直接使用count可能获取到过时的值\n  setCount(count + 1); // ❌\n  setCount(count + 1); // ❌ 这两次可能都基于同一个count值\n  \n  // 函数式更新确保每次都基于最新状态\n  setCount(prev => prev + 1); // ✅\n  setCount(prev => prev + 1); // ✅ 正确累加\n};\n```\n\n**核心原理**：React的状态更新是异步的，函数式更新确保你总是基于最新的状态值进行更新。\n\n你想看一个具体的实际应用例子吗？',
      timestamp: '2024-01-15T09:06:30Z',
      round_id: 'round2',
    },
  ],
}

const mockSummaries: { [topicId: string]: ConversationSummary[] } = {
  '1': [
    {
      id: 'summary1',
      conversation_id: 'conv1',
      round_id: 'round1',
      user_summary: '询问React Hooks学习，特别关注useState和useEffect',
      assistant_summary: '介绍了useState的基本概念和语法，提供了代码示例，询问用户想深入了解的方向',
      created_at: '2024-01-15T09:01:30Z',
      message_range: {
        start_message_id: 'msg1',
        end_message_id: 'msg2',
      },
    },
    {
      id: 'summary2',
      conversation_id: 'conv1',
      round_id: 'round2',
      user_summary: '询问useState函数式更新的使用场景',
      assistant_summary: '详细解释了函数式更新的概念、使用场景和核心原理，提供了对比代码示例',
      created_at: '2024-01-15T09:07:00Z',
      message_range: {
        start_message_id: 'msg3',
        end_message_id: 'msg4',
      },
    },
  ],
}

export const mockChatService = {
  getMessages: async (topicId: string): Promise<Message[]> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    return mockMessages[topicId] || []
  },

  sendMessage: async (topicId: string, content: string): Promise<Message> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const userMessage: Message = {
      id: `msg${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date().toISOString(),
      round_id: `round${Date.now()}`,
    }

    // 添加用户消息
    if (!mockMessages[topicId]) {
      mockMessages[topicId] = []
    }
    mockMessages[topicId].push(userMessage)

    // 模拟AI回复
    setTimeout(async () => {
      const aiResponse = await generateAIResponse(content)
      const aiMessage: Message = {
        id: `msg${Date.now() + 1}`,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date().toISOString(),
        round_id: userMessage.round_id,
      }
      
      mockMessages[topicId].push(aiMessage)
      
      // 触发摘要生成（模拟异步）
      setTimeout(() => {
        generateSummary(topicId, userMessage.round_id!, userMessage, aiMessage)
      }, 2000)
    }, 1500)

    return userMessage
  },

  getSummaries: async (topicId: string): Promise<ConversationSummary[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockSummaries[topicId] || []
  },

  getConversation: async (topicId: string, conversationId: string): Promise<Conversation> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      id: conversationId,
      topic_id: topicId,
      messages: mockMessages[topicId] || [],
      status: 'active',
      created_at: '2024-01-15T09:00:00Z',
      updated_at: new Date().toISOString(),
    }
  },
}

// 简单的AI回复生成器
const generateAIResponse = async (userInput: string): Promise<string> => {
  const responses = [
    '这是一个很好的问题！让我来详细解释一下...',
    '根据你的问题，我建议我们从以下几个方面来探讨...',
    '你提到的这个概念确实很重要。让我们通过一些例子来理解...',
    '很棒的思考！这个问题涉及到几个关键点...',
    '让我们深入分析一下这个问题...',
  ]
  
  const randomResponse = responses[Math.floor(Math.random() * responses.length)]
  
  // 根据用户输入生成更相关的回复
  if (userInput.toLowerCase().includes('hook')) {
    return `${randomResponse}\n\nReact Hooks是React 16.8引入的重要特性，它让我们可以在函数组件中使用状态和其他React特性。你想了解哪个具体的Hook呢？`
  }
  
  if (userInput.toLowerCase().includes('usestate')) {
    return `${randomResponse}\n\nuseState是最基础也是最常用的Hook之一。它的语法是：\n\`\`\`javascript\nconst [state, setState] = useState(initialValue);\n\`\`\`\n\n你有什么具体的使用场景想要讨论吗？`
  }
  
  return `${randomResponse}\n\n基于你上传的学习材料和我们之前的对话，我认为这个问题很值得深入探讨。你希望我从哪个角度来回答呢？`
}

// 生成对话摘要
const generateSummary = (topicId: string, roundId: string, userMessage: Message, aiMessage: Message) => {
  const summary: ConversationSummary = {
    id: `summary${Date.now()}`,
    conversation_id: 'conv1',
    round_id: roundId,
    user_summary: userMessage.content.length > 50 
      ? userMessage.content.substring(0, 50) + '...' 
      : userMessage.content,
    assistant_summary: aiMessage.content.length > 100 
      ? aiMessage.content.substring(0, 100) + '...' 
      : aiMessage.content,
    created_at: new Date().toISOString(),
    message_range: {
      start_message_id: userMessage.id,
      end_message_id: aiMessage.id,
    },
  }

  if (!mockSummaries[topicId]) {
    mockSummaries[topicId] = []
  }
  mockSummaries[topicId].push(summary)
}
